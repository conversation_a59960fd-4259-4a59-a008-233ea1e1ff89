<template>
  <div class="worker-profile-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="workerProfileList" :loading="tableLoading"
      :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
      operationWidth="220" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['job:worker:add']">新 增</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['job:worker:export']">导
          出</el-button>
      </template>

      <!-- 头像列插槽 -->
      <template #profilePhoto="{ row }">
        <el-avatar :size="50" :src="row.profilePhoto" :alt="row.realName" fit="cover">
          <el-icon>
            <User />
          </el-icon>
        </el-avatar>
      </template>

      <!-- 性别列插槽 -->
      <template #gender="{ row }">
        <el-tag :type="row.gender === 'male' ? 'primary' : 'danger'" size="small">
          {{ row.gender === 'male' ? '男' : '女' }}
        </el-tag>
      </template>

      <!-- 学历列插槽 -->
      <template #educationLevel="{ row }">
        <el-tag :type="getEducationTagType(row.educationLevel)" size="small">
          {{ row.educationLevel }}
        </el-tag>
      </template>

      <!-- 工作经验列插槽 -->
      <template #workExperience="{ row }">
        {{ row.workExperienceYears || 0 }}年
      </template>

      <!-- 评分列插槽 -->
      <template #rating="{ row }">
        <el-rate v-model="row.ratingAverage" disabled show-score text-color="#ff9900" score-template="{value}" :max="5"
          size="small" />
      </template>

      <!-- 完成工作列插槽 -->
      <template #completedJobs="{ row }">
        {{ row.completedJobs || 0 }}
      </template>

      <!-- 成功率列插槽 -->
      <template #successRate="{ row }">
        <span :class="getSuccessRateClass(row.successRate)">
          {{ (row.successRate || 0).toFixed(1) }}%
        </span>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 验证状态列插槽 -->
      <template #isVerified="{ row }">
        <el-tag :type="row.isVerified ? 'success' : 'info'" size="small">
          {{ row.isVerified ? '已验证' : '未验证' }}
        </el-tag>
      </template>

      <!-- 工作类别列插槽 -->
      <template #workCategories="{ row }">
        <div class="categories-container">
          <el-tag v-for="category in parseJsonArray(row.workCategories)" :key="category" size="small" type="success"
            class="category-tag">
            {{ category }}
          </el-tag>
          <span v-if="!parseJsonArray(row.workCategories).length" class="no-data">暂无类别</span>
        </div>
      </template>

      <!-- 工作类型偏好列插槽 -->
      <template #jobTypesPreferred="{ row }">
        <div class="job-types-container">
          <el-tag v-for="jobType in parseJsonArray(row.jobTypesPreferred)" :key="jobType" size="small" type="warning"
            class="job-type-tag">
            {{ jobType }}
          </el-tag>
          <span v-if="!parseJsonArray(row.jobTypesPreferred).length" class="no-data">暂无偏好</span>
        </div>
      </template>

      <!-- 技能描述列插槽 -->
      <template #skills="{ row }">
        <div class="skills-container">
          <el-tag v-for="skill in parseJsonArray(row.skills)" :key="skill" size="small" type="primary"
            class="skill-tag">
            {{ skill }}
          </el-tag>
          <span v-if="!parseJsonArray(row.skills).length" class="no-data">暂无技能</span>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['job:worker:edit']">编辑</el-button>
          <el-button v-if="!row.isVerified" type="success" link @click="handleVerify(row)"
            v-hasPermi="['job:worker:verify']">验证</el-button>
          <el-button v-if="row.status === 'active'" type="warning" link @click="handleSuspend(row)"
            v-hasPermi="['job:worker:suspend']">暂停</el-button>
          <el-button v-if="row.status !== 'active'" type="success" link @click="handleActivate(row)"
            v-hasPermi="['job:worker:activate']">激活</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['job:worker:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <WorkerProfileFormDialog ref="workerProfileFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />
  </div>
</template>

<script setup name="WorkerProfile">
import { listWorkerProfile, getWorkerProfile, delWorkerProfile, addWorkerProfile, updateWorkerProfile, activateWorkerProfile, deactivateWorkerProfile, suspendWorkerProfile, verifyWorkerProfile } from "@/api/job/worker"
import { createWorkerProfileTableOption } from "@/const/job/worker"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import { User } from '@element-plus/icons-vue'
import TableList from '@/components/TableList/index.vue'
import WorkerProfileFormDialog from './WorkerProfileFormDialog.vue'

const { proxy } = getCurrentInstance()

const workerProfileList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const isInitializing = ref(true) // 添加初始化标志

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '800px',
  dialogHeight: '60vh'
})
const tableListRef = ref(null)
const workerProfileFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    realName: undefined,
    nickname: undefined,
    phone: undefined,
    currentLocation: undefined,
    educationLevel: undefined,
    status: undefined,
    isVerified: undefined,
    createTime: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createWorkerProfileTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};

/** 查询零工信息列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listWorkerProfile(params).then(res => {
    tableLoading.value = false
    loading.value = false
    workerProfileList.value = res.rows
    total.value = res.total

    // 数据加载完成后，设置初始化完成
    nextTick(() => {
      isInitializing.value = false
    })
  })
}

// 处理搜索
const handleSearch = (params) => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params };

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {};
  Object.assign(queryParams.value, otherParams);
  queryParams.value.pageNum = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    realName: undefined,
    nickname: undefined,
    phone: undefined,
    currentLocation: undefined,
    educationLevel: undefined,
    status: undefined,
    isVerified: undefined,
    createTime: undefined
  };
  searchParams.value = {};
  getList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  isInitializing.value = true;
  queryParams.value.pageNum = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  isInitializing.value = true;
  queryParams.value.pageSize = size;
  queryParams.value.pageNum = 1;
  getList();
};

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.workerId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 查看
const handleView = (row) => {
  workerProfileFormDialogRef.value?.openDialog('view', '查看零工信息', row)
}

// 编辑
const handleEdit = (row) => {
  const workerId = row.workerId
  getWorkerProfile(workerId).then(response => {
    workerProfileFormDialogRef.value?.openDialog('edit', '编辑零工信息', response.data)
  })
}

// 新增
const handleAddWorkerProfile = () => {
  const defaultData = {
    status: "active"
  }
  workerProfileFormDialogRef.value?.openDialog('add', '新增零工信息', defaultData)
}

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
  try {
    if (payload.type === 'add') {
      // 新增
      await addWorkerProfile(payload.data)
      proxy.$modal.msgSuccess("添加成功")
    } else if (payload.type === 'edit') {
      // 编辑
      await updateWorkerProfile(payload.data)
      proxy.$modal.msgSuccess("修改成功")
    }

    // 通知子组件提交成功
    workerProfileFormDialogRef.value?.onSubmitSuccess()
    getList()
  } catch (error) {
    // 通知子组件提交失败
    workerProfileFormDialogRef.value?.onSubmitError()
    console.error('提交失败:', error)
  }
}

// 处理表单取消事件
const handleFormCancel = () => {
  // 可以在这里添加取消逻辑
}

/** 新增按钮操作 */
function handleAdd() {
  handleAddWorkerProfile()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  if (row) {
    handleEdit(row)
  } else {
    // 批量编辑
    const workerId = ids.value[0]
    const selectedRow = workerProfileList.value.find(item => item.workerId === workerId)
    if (selectedRow) {
      handleEdit(selectedRow)
    }
  }
}

/** 验证按钮操作 */
function handleVerify(row) {
  const workerIds = row?.workerId ? [row.workerId] : ids.value
  proxy.$modal.confirm('是否确认验证选中的零工信息？').then(function () {
    return verifyWorkerProfile(workerIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("验证成功")
  }).catch(() => { })
}

/** 激活按钮操作 */
function handleActivate(row) {
  const workerIds = row?.workerId ? [row.workerId] : ids.value
  proxy.$modal.confirm('是否确认激活选中的零工信息？').then(function () {
    return activateWorkerProfile(workerIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("激活成功")
  }).catch(() => { })
}

/** 暂停按钮操作 */
function handleSuspend(row) {
  const workerIds = row?.workerId ? [row.workerId] : ids.value
  proxy.$modal.confirm('是否确认暂停选中的零工信息？').then(function () {
    return suspendWorkerProfile(workerIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("暂停成功")
  }).catch(() => { })
}

/** 停用按钮操作 */
function handleDeactivate(row) {
  const workerIds = row?.workerId ? [row.workerId] : ids.value
  proxy.$modal.confirm('是否确认停用选中的零工信息？').then(function () {
    return deactivateWorkerProfile(workerIds[0])
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("停用成功")
  }).catch(() => { })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const workerIds = row?.workerId ? [row.workerId] : ids.value
  proxy.$modal.confirm('是否确认删除选中的零工信息？').then(function () {
    return delWorkerProfile(workerIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('job/worker/export', {
    ...queryParams.value
  }, `worker_profile_${new Date().getTime()}.xlsx`)
}



// 学历标签类型
function getEducationTagType(education) {
  const typeMap = {
    '博士': 'danger',
    '硕士': 'warning',
    '本科': 'success',
    '大专': 'info',
    '高中': '',
    '中专': '',
    '初中': 'info',
    '小学': 'info'
  }
  return typeMap[education] || ''
}

// 成功率样式类
function getSuccessRateClass(rate) {
  if (rate >= 90) return 'success-rate-high'
  if (rate >= 70) return 'success-rate-medium'
  if (rate >= 50) return 'success-rate-low'
  return 'success-rate-very-low'
}

// 状态标签类型
function getStatusTagType(status) {
  const typeMap = {
    'active': 'success',
    'inactive': 'info',
    'suspended': 'warning',
    'banned': 'danger'
  }
  return typeMap[status] || 'info'
}

// 状态文本
function getStatusText(status) {
  const textMap = {
    'active': '活跃',
    'inactive': '不活跃',
    'suspended': '暂停',
    'banned': '禁用'
  }
  return textMap[status] || '未知'
}

// 解析JSON数组（通用方法）
function parseJsonArray(jsonStr) {
  if (!jsonStr) return []
  try {
    // 如果已经是数组，直接返回
    if (Array.isArray(jsonStr)) {
      return jsonStr
    }
    // 如果是字符串，尝试解析JSON
    if (typeof jsonStr === 'string') {
      return JSON.parse(jsonStr)
    }
    return []
  } catch (error) {
    console.warn('解析JSON数组数据失败:', error, jsonStr)
    return []
  }
}
</script>

<style lang="scss" scoped>
/* 成功率样式 */
.success-rate-high {
  color: #67c23a;
  font-weight: bold;
}

.success-rate-medium {
  color: #e6a23c;
  font-weight: bold;
}

.success-rate-low {
  color: #f56c6c;
  font-weight: bold;
}

.success-rate-very-low {
  color: #909399;
  font-weight: bold;
}

.el-avatar {
  border: 2px solid #f0f0f0;
}

/* JSON数组字段标签样式 */
.skills-container,
.categories-container,
.job-types-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 250px;
  align-items: flex-start;
}

.skill-tag,
.category-tag,
.job-type-tag {
  margin: 0;
  font-size: 12px;
  line-height: 1.2;
  white-space: nowrap;
}

.skill-tag {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.no-data {
  color: #909399;
  font-size: 12px;
  font-style: italic;
  padding: 4px 0;
}
</style>
