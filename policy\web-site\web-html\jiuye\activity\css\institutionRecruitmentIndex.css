/* 机构招募首页样式 */

/* 招募区域特有样式 */
.recruitment-section {
    flex: 2;
    min-width: 0;
}

/* 招募列表 */
.recruitmentOut {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-top: 20px;
}

.recruitmentList {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.recruitment-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.recruitment-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.recruitment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745);
}

.recruitment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.recruitment-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
    line-height: 1.4;
    flex: 1;
    margin-right: 10px;
}

.recruitment-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
}

.status-published {
    background: #d4edda;
    color: #155724;
}

.status-ongoing {
    background: #cce5ff;
    color: #004085;
}

.status-completed {
    background: #f8d7da;
    color: #721c24;
}

.recruitment-info {
    margin-bottom: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-weight: bold;
}

.recruitment-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.recruitment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.recruitment-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-icon {
    font-size: 14px;
}

.view-btn {
    background: #007bff;
    color: white;
    padding: 6px 15px;
    border: none;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    background: #0056b3;
    transform: scale(1.05);
}

/* 机构招募特有的统计样式 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

/* 机构招募特有的响应式设计 */
@media (max-width: 768px) {
    .recruitmentList {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 底部信息条样式 */
.bottomBar {
    width: 100%;
    background: linear-gradient(135deg, #0052d9 0%, #097fcc 50%, #13aebe 100%);
    margin-top: 0px;
    padding: 20px 0;
}

.bottomContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.bottomLeft {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 40px;
}

.bottomLogo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logoText {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    line-height: 1;
}

.logoSubtext {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bottomInfo {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bottomInfo .info-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.4;
}

.bottomRight {
    display: flex;
    gap: 60px;
}

.contact-info, .service-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.contact-title, .service-title {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    margin: 0 0 8px 0;
}

.contact-item, .service-item {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .bottomContent {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .bottomLeft {
        flex-direction: column;
        gap: 20px;
    }

    .bottomRight {
        gap: 30px;
    }
}
