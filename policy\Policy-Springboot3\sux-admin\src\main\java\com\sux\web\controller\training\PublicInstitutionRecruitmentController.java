package com.sux.web.controller.training;

import com.sux.common.annotation.Anonymous;
import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.OnlineTrainingInstitutionRecruitment;
import com.sux.system.service.IOnlineTrainingInstitutionRecruitmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构招募公开API控制器
 * 提供无需登录的机构招募查询接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@Anonymous
@RequestMapping("/public/recruitment/institution")
public class PublicInstitutionRecruitmentController extends BaseController
{
    @Autowired
    private IOnlineTrainingInstitutionRecruitmentService onlineTrainingInstitutionRecruitmentService;

    /**
     * 查询机构招募列表（公开接口）
     * 只返回已发布状态的招募信息
     */
    @GetMapping("/list")
    public TableDataInfo list(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        startPage();
        // 只查询已发布的招募（状态为1）
        onlineTrainingInstitutionRecruitment.setRecruitmentStatus("1");
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(onlineTrainingInstitutionRecruitment);
        return getDataTable(list);
    }

    /**
     * 获取机构招募详细信息（公开接口）
     */
    @GetMapping(value = "/{recruitmentId}")
    public AjaxResult getInfo(@PathVariable("recruitmentId") Long recruitmentId)
    {
        OnlineTrainingInstitutionRecruitment recruitment = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(recruitmentId);
        
        if (recruitment == null) {
            return error("招募信息不存在");
        }
        
        // 只允许查看已发布的招募
        if (!"1".equals(recruitment.getRecruitmentStatus())) {
            return error("招募信息不可访问");
        }
        
        // 增加浏览次数
        try {
            onlineTrainingInstitutionRecruitmentService.incrementViewCount(recruitmentId);
        } catch (Exception e) {
            logger.warn("更新浏览次数失败: " + e.getMessage());
        }
        
        return success(recruitment);
    }

    /**
     * 获取最新的机构招募列表（公开接口）
     * 返回最新发布的招募信息，用于首页展示
     */
    @GetMapping("/latest")
    public AjaxResult getLatestRecruitments(@RequestParam(defaultValue = "5") int limit)
    {
        OnlineTrainingInstitutionRecruitment queryParam = new OnlineTrainingInstitutionRecruitment();
        queryParam.setRecruitmentStatus("1"); // 只查询已发布的
        
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectLatestRecruitments(limit);
        return success(list);
    }

    /**
     * 根据类别获取机构招募列表（公开接口）
     */
    @GetMapping("/category/{category}")
    public TableDataInfo getByCategory(@PathVariable("category") String category)
    {
        startPage();
        OnlineTrainingInstitutionRecruitment queryParam = new OnlineTrainingInstitutionRecruitment();
        queryParam.setRecruitmentStatus("1"); // 只查询已发布的
        queryParam.setTrainingCategory(category);
        
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(queryParam);
        return getDataTable(list);
    }

    /**
     * 搜索机构招募（公开接口）
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam(required = false) String keyword,
                               @RequestParam(required = false) String category,
                               @RequestParam(required = false) String level,
                               @RequestParam(required = false) String isUrgent)
    {
        startPage();
        OnlineTrainingInstitutionRecruitment queryParam = new OnlineTrainingInstitutionRecruitment();
        queryParam.setRecruitmentStatus("1"); // 只查询已发布的
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryParam.setRecruitmentTitle(keyword.trim());
        }
        if (category != null && !category.trim().isEmpty()) {
            queryParam.setTrainingCategory(category.trim());
        }
        if (level != null && !level.trim().isEmpty()) {
            queryParam.setTrainingLevel(level.trim());
        }
        if (isUrgent != null && !isUrgent.trim().isEmpty()) {
            queryParam.setIsUrgent(isUrgent.trim());
        }
        
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(queryParam);
        return getDataTable(list);
    }

    /**
     * 获取机构招募统计信息（公开接口）
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        try {
            // 总招募数量
            OnlineTrainingInstitutionRecruitment publishedParam = new OnlineTrainingInstitutionRecruitment();
            publishedParam.setRecruitmentStatus("1");
            int totalPublished = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(publishedParam).size();
            
            // 进行中的招募数量
            OnlineTrainingInstitutionRecruitment ongoingParam = new OnlineTrainingInstitutionRecruitment();
            ongoingParam.setRecruitmentStatus("2");
            int totalOngoing = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(ongoingParam).size();
            
            // 紧急招募数量
            OnlineTrainingInstitutionRecruitment urgentParam = new OnlineTrainingInstitutionRecruitment();
            urgentParam.setRecruitmentStatus("1");
            urgentParam.setIsUrgent("1");
            int totalUrgent = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(urgentParam).size();
            
            // 构建统计结果
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalPublished", totalPublished);
            statistics.put("totalOngoing", totalOngoing);
            statistics.put("totalUrgent", totalUrgent);
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取热门机构招募（公开接口）
     * 根据浏览次数和申请次数排序
     */
    @GetMapping("/popular")
    public AjaxResult getPopularRecruitments(@RequestParam(defaultValue = "10") int limit)
    {
        try {
            List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectPopularRecruitments(limit);
            return success(list);
        } catch (Exception e) {
            logger.error("获取热门招募失败", e);
            return error("获取热门招募失败");
        }
    }

    /**
     * 获取相关机构招募（公开接口）
     * 根据当前招募的类别和级别推荐相关招募
     */
    @GetMapping("/{recruitmentId}/related")
    public AjaxResult getRelatedRecruitments(@PathVariable("recruitmentId") Long recruitmentId,
                                           @RequestParam(defaultValue = "5") int limit)
    {
        try {
            OnlineTrainingInstitutionRecruitment current = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(recruitmentId);
            if (current == null) {
                return error("招募信息不存在");
            }
            
            List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectRelatedRecruitments(
                current.getTrainingCategory(), 
                current.getTrainingLevel(), 
                recruitmentId, 
                limit
            );
            return success(list);
        } catch (Exception e) {
            logger.error("获取相关招募失败", e);
            return error("获取相关招募失败");
        }
    }
}
