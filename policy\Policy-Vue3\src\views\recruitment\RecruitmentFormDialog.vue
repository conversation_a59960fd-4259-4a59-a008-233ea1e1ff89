<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    :width="formOption.dialogWidth || '1200px'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="24">
          <el-divider content-position="left">基本信息</el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="招募标题" prop="recruitmentTitle">
            <el-input v-model="formData.recruitmentTitle" placeholder="请输入招募标题" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训类别" prop="trainingCategory">
            <el-select v-model="formData.trainingCategory" placeholder="请选择培训类别" :disabled="isView" style="width: 100%">
              <el-option label="IT技能" value="IT技能" />
              <el-option label="管理培训" value="管理培训" />
              <el-option label="职业技能" value="职业技能" />
              <el-option label="语言培训" value="语言培训" />
              <el-option label="财务培训" value="财务培训" />
              <el-option label="营销培训" value="营销培训" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训级别" prop="trainingLevel">
            <el-select v-model="formData.trainingLevel" placeholder="请选择培训级别" :disabled="isView" style="width: 100%">
              <el-option label="初级" value="初级" />
              <el-option label="中级" value="中级" />
              <el-option label="高级" value="高级" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训时长(小时)" prop="trainingDuration">
            <el-input-number v-model="formData.trainingDuration" :min="1" :max="1000" placeholder="请输入培训时长" :disabled="isView" style="width: 100%" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="最大参与人数" prop="maxParticipants">
            <el-input-number v-model="formData.maxParticipants" :min="1" :max="1000" placeholder="请输入最大参与人数" :disabled="isView" style="width: 100%" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训地点" prop="trainingLocation">
            <el-input v-model="formData.trainingLocation" placeholder="请输入培训地点" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="招募描述" prop="recruitmentDescription">
            <el-input v-model="formData.recruitmentDescription" type="textarea" :rows="3" placeholder="请输入招募描述" :disabled="isView" />
          </el-form-item>
        </el-col>

        <!-- 费用信息 -->
        <el-col :span="24">
          <el-divider content-position="left">费用信息</el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训费用最低价" prop="trainingFeeMin">
            <el-input-number v-model="formData.trainingFeeMin" :min="0" :precision="2" placeholder="请输入最低价" :disabled="isView" style="width: 100%" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="培训费用最高价" prop="trainingFeeMax">
            <el-input-number v-model="formData.trainingFeeMax" :min="0" :precision="2" placeholder="请输入最高价" :disabled="isView" style="width: 100%" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="预算金额" prop="budgetAmount">
            <el-input-number v-model="formData.budgetAmount" :min="0" :precision="2" placeholder="请输入预算金额" :disabled="isView" style="width: 100%" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="付款方式" prop="paymentMethod">
            <el-select v-model="formData.paymentMethod" placeholder="请选择付款方式" :disabled="isView" style="width: 100%">
              <el-option label="一次性付款" value="一次性付款" />
              <el-option label="分期付款" value="分期付款" />
              <el-option label="按阶段付款" value="按阶段付款" />
              <el-option label="培训后付款" value="培训后付款" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 培训方式 -->
        <el-col :span="24">
          <el-divider content-position="left">培训方式</el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="支持线上培训">
            <el-switch v-model="formData.onlineSupport" active-value="1" inactive-value="0" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="支持线下培训">
            <el-switch v-model="formData.offlineSupport" active-value="1" inactive-value="0" :disabled="isView" />
          </el-form-item>
        </el-col>

        <!-- 时间安排 -->
        <el-col :span="24">
          <el-divider content-position="left">时间安排</el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="申请开始时间" prop="applicationStartDate">
            <el-date-picker
              v-model="formData.applicationStartDate"
              type="datetime"
              placeholder="请选择申请开始时间"
              :disabled="isView"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="申请截止时间" prop="applicationEndDate">
            <el-date-picker
              v-model="formData.applicationEndDate"
              type="datetime"
              placeholder="请选择申请截止时间"
              :disabled="isView"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="预期开始时间" prop="expectedStartDate">
            <el-date-picker
              v-model="formData.expectedStartDate"
              type="datetime"
              placeholder="请选择预期开始时间"
              :disabled="isView"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="预期结束时间" prop="expectedEndDate">
            <el-date-picker
              v-model="formData.expectedEndDate"
              type="datetime"
              placeholder="请选择预期结束时间"
              :disabled="isView"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 联系信息 -->
        <el-col :span="24">
          <el-divider content-position="left">联系信息</el-divider>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="联系邮箱" prop="contactEmail">
            <el-input v-model="formData.contactEmail" placeholder="请输入联系邮箱" :disabled="isView" />
          </el-form-item>
        </el-col>

        <!-- 要求描述 -->
        <el-col :span="24">
          <el-divider content-position="left">要求描述</el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="机构资质要求" prop="qualificationRequirements">
            <el-input v-model="formData.qualificationRequirements" type="textarea" :rows="3" placeholder="请输入机构资质要求" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="经验要求" prop="experienceRequirements">
            <el-input v-model="formData.experienceRequirements" type="textarea" :rows="3" placeholder="请输入经验要求" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="师资要求" prop="teacherRequirements">
            <el-input v-model="formData.teacherRequirements" type="textarea" :rows="3" placeholder="请输入师资要求" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="设施设备要求" prop="facilityRequirements">
            <el-input v-model="formData.facilityRequirements" type="textarea" :rows="3" placeholder="请输入设施设备要求" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="评选标准" prop="evaluationCriteria">
            <el-input v-model="formData.evaluationCriteria" type="textarea" :rows="3" placeholder="请输入评选标准" :disabled="isView" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="其他要求" prop="additionalRequirements">
            <el-input v-model="formData.additionalRequirements" type="textarea" :rows="3" placeholder="请输入其他要求" :disabled="isView" />
          </el-form-item>
        </el-col>

        <!-- 状态设置 -->
        <el-col :span="24" v-if="!isView">
          <el-divider content-position="left">状态设置</el-divider>
        </el-col>
        
        <el-col :span="8" v-if="!isView">
          <el-form-item label="是否推荐">
            <el-switch v-model="formData.isFeatured" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-col>
        
        <el-col :span="8" v-if="!isView">
          <el-form-item label="是否紧急">
            <el-switch v-model="formData.isUrgent" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-col>
        
        <el-col :span="24" v-if="!isView">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading" v-if="!isView">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'

// Props
const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('add') // add, edit, view
const formLoading = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = reactive({
  recruitmentId: undefined,
  recruitmentTitle: '',
  recruitmentDescription: '',
  trainingCategory: '',
  trainingLevel: '',
  trainingDuration: undefined,
  maxParticipants: undefined,
  trainingFeeMin: undefined,
  trainingFeeMax: undefined,
  trainingLocation: '',
  onlineSupport: '0',
  offlineSupport: '0',
  qualificationRequirements: '',
  experienceRequirements: '',
  teacherRequirements: '',
  facilityRequirements: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  applicationStartDate: undefined,
  applicationEndDate: undefined,
  expectedStartDate: undefined,
  expectedEndDate: undefined,
  recruitmentStatus: '0',
  isFeatured: '0',
  isUrgent: '0',
  budgetAmount: undefined,
  paymentMethod: '',
  evaluationCriteria: '',
  additionalRequirements: '',
  remark: ''
})

// 表单验证规则
const formRules = reactive({
  recruitmentTitle: [
    { required: true, message: '招募标题不能为空', trigger: 'blur' }
  ],
  trainingCategory: [
    { required: true, message: '培训类别不能为空', trigger: 'change' }
  ],
  contactPerson: [
    { required: true, message: '联系人不能为空', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
})

// 计算属性
const isView = computed(() => dialogType.value === 'view')

// 方法
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title
  dialogVisible.value = true
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (data[key] !== undefined) {
      formData[key] = data[key]
    } else {
      // 设置默认值
      if (key === 'onlineSupport' || key === 'offlineSupport' || key === 'isFeatured' || key === 'isUrgent') {
        formData[key] = '0'
      } else if (key === 'recruitmentStatus') {
        formData[key] = '0'
      } else {
        formData[key] = undefined
      }
    }
  })
  
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // 发送提交事件
    emit('submit', {
      type: dialogType.value,
      data: { ...formData }
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 暴露方法给父组件
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
}
</style>
