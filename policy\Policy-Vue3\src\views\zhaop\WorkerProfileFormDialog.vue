<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth || '800px'"
    :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>

    <div class="form-container" :style="{ maxHeight: formOption.dialogHeight || '60vh', overflowY: 'auto' }">
      <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="formOption.labelWidth || '100px'">
        <el-row :gutter="20">
          <template v-for="field in formFields" :key="field.prop">
            <!-- 分隔线 -->
            <el-col v-if="field.divider" :span="24" class="divider-col">
              <el-divider content-position="left">{{ field.label }}</el-divider>
            </el-col>

            <!-- 普通表单项 -->
            <el-col v-else :span="field.span || 12" class="form-col">
              <el-form-item :label="field.label" :prop="field.prop" :rules="field.rules">
                <!-- 技能标签输入 (优先处理 formslot) -->
                <div v-if="field.prop === 'skills' && field.formslot" class="skills-input-container">
                  <div class="skills-tags">
                    <el-tag
                      v-for="(skill, index) in getSkillsArray()"
                      :key="index"
                      closable
                      :disable-transitions="false"
                      @close="removeSkill(index)"
                      type="primary"
                      size="small"
                      class="skill-tag"
                    >
                      {{ skill }}
                    </el-tag>
                  </div>
                  <el-input
                    v-if="!isViewMode"
                    v-model="newSkill"
                    size="small"
                    placeholder="输入技能后按回车添加"
                    class="skill-input"
                    @keyup.enter="addSkill"
                    @blur="addSkill"
                    clearable
                  />
                  <div v-if="isViewMode && getSkillsArray().length === 0" class="no-skills">
                    暂无技能
                  </div>
                </div>

                <!-- 输入框 -->
                <el-input
                  v-else-if="!field.type || field.type === 'input'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                  clearable
                />

                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  :rows="field.minRows || 4"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                  resize="vertical"
                />

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  :min="field.min"
                  :max="field.max"
                  :precision="field.precision"
                  :step="field.step || 1"
                  style="width: 100%"
                />

                <!-- 选择器 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in field.dicData"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>

                <!-- 开关 -->
                <el-switch
                  v-else-if="field.type === 'switch'"
                  v-model="formData[field.prop]"
                  :disabled="isViewMode || field.disabled"
                  active-value="0"
                  inactive-value="1"
                />

                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  v-model="formData[field.prop]"
                  type="date"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  style="width: 100%"
                />

                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'datetime'"
                  v-model="formData[field.prop]"
                  type="datetime"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  style="width: 100%"
                />

                <!-- 评分显示 -->
                <el-rate
                  v-else-if="field.type === 'rate'"
                  v-model="formData[field.prop]"
                  :disabled="true"
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                  :max="5"
                />



                <!-- 头像上传 -->
                <el-upload
                  v-else-if="field.prop === 'profilePhoto'"
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :disabled="isViewMode"
                >
                  <img v-if="formData[field.prop]" :src="formData[field.prop]" class="avatar" />
                  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button v-if="!isViewMode" type="primary" :loading="submitLoading" @click="handleSubmit">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('add') // add, edit, view
const submitLoading = ref(false)
const formRef = ref(null)
const formData = reactive({})
const originalData = ref({})
const newSkill = ref('')

// 计算属性
const isViewMode = computed(() => dialogType.value === 'view')

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.formFields.forEach(field => {
    if (field.rules && field.prop) {
      rules[field.prop] = field.rules
    }
  })
  return rules
})

// 头像上传前的检查
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 获取技能数组
const getSkillsArray = () => {
  if (!formData.skills) return []
  try {
    // 如果已经是数组，直接返回
    if (Array.isArray(formData.skills)) {
      return formData.skills
    }
    // 如果是字符串，尝试解析JSON
    if (typeof formData.skills === 'string') {
      return JSON.parse(formData.skills)
    }
    return []
  } catch (error) {
    console.warn('解析技能数组失败:', error)
    return []
  }
}

// 添加技能
const addSkill = () => {
  const skill = newSkill.value?.trim()
  if (!skill) return

  const currentSkills = getSkillsArray()
  if (!currentSkills.includes(skill)) {
    const updatedSkills = [...currentSkills, skill]
    formData.skills = JSON.stringify(updatedSkills)
  }
  newSkill.value = ''
}

// 移除技能
const removeSkill = (index) => {
  const currentSkills = getSkillsArray()
  currentSkills.splice(index, 1)
  formData.skills = JSON.stringify(currentSkills)
}

// 打开弹窗
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title

  // 重置表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })

  // 设置表单数据
  Object.assign(formData, data)
  originalData.value = { ...data }

  // 重置新技能输入
  newSkill.value = ''

  dialogVisible.value = true

  // 重置表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return

  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true

      // 发送提交事件
      emit('submit', {
        type: dialogType.value,
        data: { ...formData }
      })
    }
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 暴露方法
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style lang="scss" scoped>
.form-container {
  padding: 0 10px;
}

.divider-col {
  margin: 10px 0;

  .el-divider {
    margin: 15px 0;

    :deep(.el-divider__text) {
      font-weight: 600;
      color: #409eff;
    }
  }
}

.form-col {
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
  border-radius: 6px;
}

.avatar-uploader :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
}

.avatar-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

/* 技能输入样式 */
.skills-input-container {
  .skills-tags {
    margin-bottom: 8px;
    min-height: 32px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
  }

  .skill-tag {
    margin: 0;
    font-size: 12px;
  }

  .skill-input {
    width: 100%;
  }

  .no-skills {
    color: #909399;
    font-size: 14px;
    font-style: italic;
    padding: 8px 0;
  }
}
</style>
