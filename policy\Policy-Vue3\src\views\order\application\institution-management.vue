<template>
  <div class="institution-application-management app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="applicationList" :loading="tableLoading"
      :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
      operationWidth="250" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleRefresh">刷新</el-button>
      </template>

      <!-- 申请状态列插槽 -->
      <template #applicationStatus="{ row }">
        <el-tag :type="getStatusTagType(row.applicationStatus)">
          {{ getStatusText(row.applicationStatus) }}
        </el-tag>
      </template>

      <!-- 申请时间列插槽 -->
      <template #applicationTime="{ row }">
        {{ formatDateTime(row.applicationTime) }}
      </template>

      <!-- 审核时间列插槽 -->
      <template #reviewTime="{ row }">
        {{ formatDateTime(row.reviewTime) }}
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button v-if="row.applicationStatus === '0'" type="success" link @click="handleReview(row, '1')"
            v-hasPermi="['training:institution:application:review']">通过</el-button>
          <el-button v-if="row.applicationStatus === '0'" type="warning" link @click="handleReview(row, '2')"
            v-hasPermi="['training:institution:application:review']">拒绝</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialog.visible" :title="detailDialog.title" width="1000px" append-to-body destroy-on-close>
      <div v-if="detailDialog.data" class="application-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="机构名称">{{ detailDialog.data.institutionName || '--' }}</el-descriptions-item>
            <el-descriptions-item label="机构代码">{{ detailDialog.data.institutionCode || '--' }}</el-descriptions-item>
            <el-descriptions-item label="法定代表人">{{ detailDialog.data.legalPerson || '--' }}</el-descriptions-item>
            <el-descriptions-item label="机构类型">{{ detailDialog.data.institutionType || '--' }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ detailDialog.data.contactPerson || '--' }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ detailDialog.data.contactPhone || '--' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ detailDialog.data.contactEmail || '--' }}</el-descriptions-item>
            <el-descriptions-item label="成立时间">{{ formatDateTime(detailDialog.data.establishedDate) }}</el-descriptions-item>
            <el-descriptions-item label="注册资本">{{ detailDialog.data.registeredCapital ? detailDialog.data.registeredCapital + '万元' : '--' }}</el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="getStatusTagType(detailDialog.data.applicationStatus)">
                {{ getStatusText(detailDialog.data.applicationStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ formatDateTime(detailDialog.data.applicationTime) }}</el-descriptions-item>
            <el-descriptions-item label="机构地址" :span="2">{{ detailDialog.data.institutionAddress || '--' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 培训能力信息 -->
        <div class="detail-section">
          <h4 class="section-title">培训能力</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="经营范围">{{ detailDialog.data.businessScope || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训经验">{{ detailDialog.data.trainingExperience || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训能力">{{ detailDialog.data.trainingCapacity || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训计划">{{ detailDialog.data.trainingPlan || '--' }}</el-descriptions-item>
            <el-descriptions-item label="师资信息">{{ detailDialog.data.teacherInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item label="设施设备">{{ detailDialog.data.facilityInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="detailDialog.data.applicationNote" label="申请备注">{{ detailDialog.data.applicationNote }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 申请材料 -->
        <div class="detail-section">
          <h4 class="section-title">申请材料</h4>
          <div class="uploaded-materials">
            <div class="material-item" v-for="(material, index) in viewMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <div class="material-name">
                    <el-icon class="material-icon">
                      <Document />
                    </el-icon>
                    <span>{{ material.name }}</span>
                  </div>
                  <div class="material-status">
                    <el-tag v-if="material.files && material.files.length > 0" type="success" size="small">
                      已上传 {{ material.files.length }} 个文件
                    </el-tag>
                    <el-tag v-else type="info" size="small">未上传</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-files" v-if="material.files && material.files.length > 0">
                <div class="file-grid">
                  <div class="file-card" v-for="(file, fileIndex) in material.files" :key="fileIndex">
                    <FileView :file="{ filePath: file.url || file.filePath, sourceFileName: file.name || file.fileName }" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="detail-section" v-if="detailDialog.data.applicationStatus !== '0'">
          <h4 class="section-title">审核信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="审核时间">{{ formatDateTime(detailDialog.data.reviewTime) }}</el-descriptions-item>
            <el-descriptions-item label="审核人">{{ detailDialog.data.reviewer || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="detailDialog.data.reviewComment" label="审核意见" :span="2">{{ detailDialog.data.reviewComment }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog v-model="reviewDialog.visible" :title="reviewDialog.title" width="500px" append-to-body>
      <el-form ref="reviewFormRef" :model="reviewDialog.form" label-width="80px">
        <el-form-item label="审核状态">
          <el-radio-group v-model="reviewDialog.form.status">
            <el-radio value="1">通过</el-radio>
            <el-radio value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input v-model="reviewDialog.form.reviewComment" type="textarea" :rows="4"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reviewDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitReview">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InstitutionApplicationManagement">
import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
import { Document } from '@element-plus/icons-vue'
import {
  listInstitutionRecruitmentApplications,
  delInstitutionRecruitmentApplication,
  reviewInstitutionRecruitmentApplication
} from "@/api/recruitment/institutionApplication"
import TableList from '@/components/TableList/index.vue'
import FileView from '@/components/FileView/index.vue'

const { proxy } = getCurrentInstance()

const applicationList = ref([])
const loading = ref(true)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const tableListRef = ref(null)
const searchParams = ref({})

// 审核弹窗
const reviewDialog = ref({
  visible: false,
  title: '',
  form: {
    applicationId: null,
    status: '1',
    reviewComment: ''
  }
})

// 详情弹窗
const detailDialog = ref({
  visible: false,
  title: '查看机构申请详情',
  data: {}
})

// 查看材料列表（用于详情弹窗）
const viewMaterials = ref([
  {
    name: '机构营业执照或组织机构代码证',
    files: [],
    field: 'qualificationFiles'
  },
  {
    name: '培训计划详细方案',
    files: [],
    field: 'trainingPlanFile'
  },
  {
    name: '师资队伍资质证明材料',
    files: [],
    field: 'teacherCertFiles'
  },
  {
    name: '培训场地及设施设备证明',
    files: [],
    field: 'facilityFiles'
  },
  {
    name: '其他相关资质证明材料',
    files: [],
    field: 'otherFiles'
  }
])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recruitmentId: undefined, // 招募ID
    institutionName: undefined,
    contactPhone: undefined,
    applicationStatus: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 定义表格列配置
    tableColumns.value = [
      { prop: 'recruitmentTitle', label: '招募标题', minWidth: 200, showOverflowTooltip: true },
      { prop: 'institutionName', label: '机构名称', minWidth: 180, showOverflowTooltip: true },
      { prop: 'contactPerson', label: '联系人', width: 120 },
      { prop: 'contactPhone', label: '联系电话', width: 150 },
      { prop: 'contactEmail', label: '联系邮箱', minWidth: 180, showOverflowTooltip: true },
      { prop: 'institutionAddress', label: '机构地址', minWidth: 200, showOverflowTooltip: true },
      { prop: 'applicationStatus', label: '申请状态', width: 120, slot: true },
      { prop: 'applicationTime', label: '申请时间', width: 150, slot: true },
      { prop: 'reviewTime', label: '审核时间', width: 150, slot: true },
      { prop: 'reviewer', label: '审核人', width: 120 }
    ]

    // 定义可搜索的字段
    searchableColumns.value = [
      { prop: 'recruitmentTitle', label: '招募标题', type: 'input' },
      { prop: 'institutionName', label: '机构名称', type: 'input' },
      { prop: 'contactPhone', label: '联系电话', type: 'input' },
      {
        prop: 'applicationStatus',
        label: '申请状态',
        type: 'select',
        options: [
          { label: '待审核', value: '0' },
          { label: '已通过', value: '1' },
          { label: '已拒绝', value: '2' },
          { label: '已取消', value: '3' }
        ]
      }
    ]

    isTableReady.value = true
  } catch (error) {
    isTableReady.value = false
    console.error('初始化配置失败:', error)
  }
}

/** 查询机构招募申请列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listInstitutionRecruitmentApplications(params).then(res => {
    tableLoading.value = false
    loading.value = false
    applicationList.value = res.rows
    total.value = res.total
  }).catch(() => {
    tableLoading.value = false
    loading.value = false
  })
}

// 处理搜索
const handleSearch = (params) => {
  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params }

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {}
  Object.assign(queryParams.value, otherParams)
  queryParams.value.pageNum = 1
  getList()
}

// 重置搜索
const resetSearch = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    recruitmentId: undefined,
    institutionName: undefined,
    contactPhone: undefined,
    applicationStatus: undefined
  }
  searchParams.value = {}
  getList()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  getList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  getList()
}

// 处理选择变化
const handleSelectionChange = () => {
  // 可以在这里处理选择逻辑
}

// 刷新数据
const handleRefresh = () => {
  getList()
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  if (!status && status !== '0') return 'info'
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[String(status)] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  if (!status && status !== '0') return '未知'
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[String(status)] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return proxy.parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 查看详情
const handleView = (row) => {
  detailDialog.value.visible = true
  detailDialog.value.data = { ...row }

  // 处理文件数据
  viewMaterials.value.forEach(material => {
    const fileData = row[material.field]
    console.log(`${material.name} 文件数据:`, fileData)

    if (fileData) {
      try {
        let parsedFiles = JSON.parse(fileData) || []
        // 确保文件对象包含所有必要字段
        material.files = parsedFiles.map(file => ({
          name: file.name || file.fileName || file.sourceFileName,
          fileName: file.fileName || file.name || file.sourceFileName,
          sourceFileName: file.sourceFileName || file.name || file.fileName,
          url: file.url || file.filePath,
          filePath: file.filePath || file.url
        }))
        console.log(`${material.name} 解析后文件:`, material.files)
      } catch (error) {
        console.error(`${material.name} JSON解析失败:`, error)
        material.files = []
      }
    } else {
      material.files = []
    }
  })
}

// 删除按钮操作
function handleDelete(row) {
  const applicationIds = row.applicationId
  proxy.$modal.confirm('是否确认删除机构"' + row.institutionName + '"的申请记录？').then(function () {
    return delInstitutionRecruitmentApplication(applicationIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 审核按钮操作 */
function handleReview(row, status) {
  reviewDialog.value.visible = true
  reviewDialog.value.title = status === '1' ? '通过审核' : '拒绝审核'
  reviewDialog.value.form.applicationId = row.applicationId
  reviewDialog.value.form.status = status
  reviewDialog.value.form.reviewComment = ''
}

/** 提交审核 */
function submitReview() {
  const form = reviewDialog.value.form
  reviewInstitutionRecruitmentApplication(form.applicationId, form.status, form.reviewComment).then(() => {
    reviewDialog.value.visible = false
    getList()
    proxy.$modal.msgSuccess("审核成功")
  }).catch(() => { })
}
</script>

<style lang="scss" scoped>
.institution-application-management {
  .operation-btns {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
}

// 详情弹窗样式
.application-detail {
  .detail-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }
  }
}

// 查看详情中的材料展示样式
.uploaded-materials {
  .material-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .material-header {
      margin-bottom: 15px;

      .material-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #2d3748;
          font-size: 15px;

          .material-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 18px;
          }
        }
      }
    }

    .material-files {
      .file-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        .file-card {
          background: rgba(248, 250, 252, 0.9);
          border: 1px solid rgba(226, 232, 240, 0.8);
          border-radius: 12px;
          padding: 12px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}
</style>
