package com.sux.system.service;

import com.sux.system.domain.OnlineTrainingInstitutionRecruitment;

import java.util.List;

/**
 * 线上招募培训机构发布Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IOnlineTrainingInstitutionRecruitmentService 
{
    /**
     * 查询线上招募培训机构发布
     * 
     * @param recruitmentId 线上招募培训机构发布主键
     * @return 线上招募培训机构发布
     */
    public OnlineTrainingInstitutionRecruitment selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(Long recruitmentId);

    /**
     * 查询线上招募培训机构发布列表
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 线上招募培训机构发布集合
     */
    public List<OnlineTrainingInstitutionRecruitment> selectOnlineTrainingInstitutionRecruitmentList(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment);

    /**
     * 新增线上招募培训机构发布
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 结果
     */
    public int insertOnlineTrainingInstitutionRecruitment(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment);

    /**
     * 修改线上招募培训机构发布
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 结果
     */
    public int updateOnlineTrainingInstitutionRecruitment(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment);

    /**
     * 批量删除线上招募培训机构发布
     * 
     * @param recruitmentIds 需要删除的线上招募培训机构发布主键集合
     * @return 结果
     */
    public int deleteOnlineTrainingInstitutionRecruitmentByRecruitmentIds(Long[] recruitmentIds);

    /**
     * 删除线上招募培训机构发布信息
     * 
     * @param recruitmentId 线上招募培训机构发布主键
     * @return 结果
     */
    public int deleteOnlineTrainingInstitutionRecruitmentByRecruitmentId(Long recruitmentId);

    /**
     * 发布招募信息
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    public int publishRecruitment(Long recruitmentId);

    /**
     * 取消招募信息
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    public int cancelRecruitment(Long recruitmentId);

    /**
     * 增加浏览次数
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    public int incrementViewCount(Long recruitmentId);

    /**
     * 增加申请次数
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    public int incrementApplicationCount(Long recruitmentId);

    /**
     * 更新选中机构数量
     * 
     * @param recruitmentId 招募ID
     * @param selectedCount 选中数量
     * @return 结果
     */
    public int updateSelectedCount(Long recruitmentId, Integer selectedCount);

    /**
     * 获取招募统计信息
     * 
     * @return 统计信息
     */
    public List<OnlineTrainingInstitutionRecruitment> selectRecruitmentStats();

    /**
     * 批量更新招募状态
     *
     * @param recruitmentIds 招募ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateRecruitmentStatus(Long[] recruitmentIds, String status);

    /**
     * 获取最新的机构招募列表
     *
     * @param limit 限制数量
     * @return 招募列表
     */
    public List<OnlineTrainingInstitutionRecruitment> selectLatestRecruitments(int limit);

    /**
     * 获取热门机构招募列表
     *
     * @param limit 限制数量
     * @return 招募列表
     */
    public List<OnlineTrainingInstitutionRecruitment> selectPopularRecruitments(int limit);

    /**
     * 获取相关机构招募列表
     *
     * @param category 培训类别
     * @param level 培训级别
     * @param excludeId 排除的招募ID
     * @param limit 限制数量
     * @return 招募列表
     */
    public List<OnlineTrainingInstitutionRecruitment> selectRelatedRecruitments(String category, String level, Long excludeId, int limit);
}
