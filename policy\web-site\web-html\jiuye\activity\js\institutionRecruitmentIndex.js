// 机构招募首页JavaScript
// 公用模块html
headerBar()
footerBar()

// 全局变量
var recruitmentData = [];
var currentPage = 1;
var pageSize = 6; // 首页显示6个招募项目

// 页面初始化
function initInstitutionRecruitmentIndexPage() {
    console.log('机构招募首页初始化开始');

    // 加载招募数据
    loadRecruitmentData();

    // 加载统计数据
    loadRecruitmentStats();

    console.log('机构招募首页初始化完成');
}

// 加载招募数据
function loadRecruitmentData() {
    console.log('开始加载招募数据');

    // 尝试调用API，失败时使用模拟数据
    console.log('尝试加载机构招募数据...');

    institutionRecruitmentAjaxRequest('public/recruitment/institution/list', {
        pageNum: currentPage,
        pageSize: pageSize,
        status: '1' // 只显示已发布的招募
    }, function(data) {
        if (data.code == 0 || data.code == 200) {
            recruitmentData = data.rows || [];
            renderRecruitmentList(recruitmentData);
            updateTotalCount(data.total || 0);
        } else {
            console.error('加载招募数据失败：', data.msg);
            console.log('使用模拟数据进行测试');
            loadMockRecruitmentData();
        }
    });
}

// 加载模拟招募数据
function loadMockRecruitmentData() {
    console.log('使用模拟招募数据');
    
    var mockData = [
        {
            recruitmentId: 1,
            recruitmentTitle: '职业技能培训机构招募',
            recruitmentDescription: '面向全市招募具备职业技能培训资质的优质机构，共同打造技能人才培养体系。',
            trainingCategory: '职业技能',
            trainingLevel: '中级',
            maxParticipants: 30,
            recruitmentStatus: '1',
            createTime: '2025-07-20 10:00:00',
            applicationEndDate: '2025-08-20 23:59:59',
            viewCount: 156,
            applicationCount: 8,
            selectedCount: 2
        },
        {
            recruitmentId: 2,
            recruitmentTitle: '企业管理培训机构合作招募',
            recruitmentDescription: '寻找专业的企业管理培训机构，为本地企业提供高质量的管理培训服务。',
            trainingCategory: '企业管理',
            trainingLevel: '高级',
            maxParticipants: 25,
            recruitmentStatus: '1',
            createTime: '2025-07-18 14:30:00',
            applicationEndDate: '2025-08-15 23:59:59',
            viewCount: 89,
            applicationCount: 4,
            selectedCount: 1
        },
        {
            recruitmentId: 3,
            recruitmentTitle: '技术认证培训机构招募',
            recruitmentDescription: '招募具备国际技术认证培训资质的机构，提供前沿技术培训课程。',
            trainingCategory: '技术认证',
            trainingLevel: '高级',
            maxParticipants: 40,
            recruitmentStatus: '1',
            createTime: '2025-07-15 09:15:00',
            applicationEndDate: '2025-08-10 23:59:59',
            viewCount: 234,
            applicationCount: 12,
            selectedCount: 3
        }
    ];
    
    recruitmentData = mockData;
    renderRecruitmentList(recruitmentData);
    updateTotalCount(mockData.length);
}

// 渲染招募列表
function renderRecruitmentList(data) {
    var listContainer = $('.recruitmentList');
    var noDataContainer = $('.nodataPic');
    
    if (!data || data.length === 0) {
        listContainer.hide();
        noDataContainer.show();
        return;
    }
    
    noDataContainer.hide();
    listContainer.show();
    
    var html = '';
    data.forEach(function(item) {
        var statusClass = getStatusClass(item.recruitmentStatus);
        var statusText = getStatusText(item.recruitmentStatus);

        html += '<li class="recruitment-item" onclick="goInstitutionRecruitmentDetail(' + item.recruitmentId + ')">';
        html += '    <div class="recruitment-header">';
        html += '        <h3 class="recruitment-title">' + (item.recruitmentTitle || '未知标题') + '</h3>';
        html += '        <span class="recruitment-status ' + statusClass + '">' + statusText + '</span>';
        html += '    </div>';
        html += '    <div class="recruitment-info">';
        html += '        <div class="info-row">';
        html += '            <span class="info-label">培训类别:</span>';
        html += '            <span class="info-value">' + (item.trainingCategory || '未分类') + '</span>';
        html += '        </div>';
        html += '        <div class="info-row">';
        html += '            <span class="info-label">培训级别:</span>';
        html += '            <span class="info-value">' + (item.trainingLevel || '未知') + '</span>';
        html += '        </div>';
        html += '        <div class="info-row">';
        html += '            <span class="info-label">最大参与人数:</span>';
        html += '            <span class="info-value">' + (item.maxParticipants || 0) + '人</span>';
        html += '        </div>';
        html += '    </div>';
        html += '    <div class="recruitment-description">' + (item.recruitmentDescription || '暂无描述') + '</div>';
        html += '    <div class="recruitment-footer">';
        html += '        <div class="recruitment-stats">';
        html += '            <div class="stat-item">';
        html += '                <span class="stat-icon">👁️</span>';
        html += '                <span>' + (item.viewCount || 0) + '</span>';
        html += '            </div>';
        html += '            <div class="stat-item">';
        html += '                <span class="stat-icon">📝</span>';
        html += '                <span>' + (item.applicationCount || 0) + '</span>';
        html += '            </div>';
        html += '            <div class="stat-item">';
        html += '                <span class="stat-icon">✅</span>';
        html += '                <span>' + (item.selectedCount || 0) + '</span>';
        html += '            </div>';
        html += '        </div>';
        html += '        <button class="view-btn" onclick="event.stopPropagation(); goInstitutionRecruitmentDetail(' + item.recruitmentId + ')">查看详情</button>';
        html += '    </div>';
        html += '</li>';
    });
    
    listContainer.html(html);
}

// 获取状态样式类
function getStatusClass(status) {
    switch(status) {
        case '0': return 'status-draft';
        case '1': return 'status-published';
        case '2': return 'status-ongoing';
        case '3': return 'status-completed';
        case '4': return 'status-cancelled';
        default: return 'status-unknown';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch(status) {
        case '0': return '草稿';
        case '1': return '已发布';
        case '2': return '进行中';
        case '3': return '已完成';
        case '4': return '已取消';
        default: return '未知';
    }
}

// 加载统计数据
function loadRecruitmentStats() {
    console.log('开始加载统计数据');

    // 尝试调用统计API，失败时使用模拟数据
    console.log('尝试加载统计数据...');

    institutionRecruitmentAjaxRequest('public/recruitment/institution/statistics', {}, function(data) {
        if (data.code == 0 || data.code == 200) {
            var stats = data.data || {};
            updateStatsDisplay(stats);
        } else {
            console.error('加载统计数据失败：', data.msg);
            console.log('使用模拟统计数据进行测试');
            loadMockStats();
        }
    });
}

// 加载模拟统计数据
function loadMockStats() {
    var mockStats = {
        totalRecruitments: 15,
        ongoingRecruitments: 8,
        totalApplications: 45,
        selectedInstitutions: 12,
        monthlyCount: 3
    };
    updateStatsDisplay(mockStats);
}

// 更新统计显示
function updateStatsDisplay(stats) {
    $('#totalRecruitments').text(stats.totalRecruitments || 0);
    $('#ongoingRecruitments').text(stats.ongoingRecruitments || 0);
    $('#totalApplications').text(stats.totalApplications || 0);
    $('#selectedInstitutions').text(stats.selectedInstitutions || 0);
    $('#monthlyCount').text(stats.monthlyCount || 0);
}

// 更新总数显示
function updateTotalCount(total) {
    $('#totalCount').text(total);
}

// 跳转到招募列表页面
function goInstitutionRecruitmentList() {
    window.open('institutionRecruitmentList.html');
}

// 跳转到招募详情页面
function goInstitutionRecruitmentDetail(recruitmentId) {
    window.open('institutionRecruitmentDetail.html?id=' + recruitmentId);
}

// 按状态筛选
function filterByStatus(status) {
    var url = 'institutionRecruitmentList.html';
    if (status) {
        url += '?status=' + status;
    }
    window.open(url);
}

// 机构招募Ajax请求函数（复用现有的函数）
function institutionRecruitmentAjaxRequest(url, params, callback) {
    var baseUrl = 'http://************/sux-admin/';
    // var baseUrl = 'http://localhost:80/sux-admin/';

    // 构建查询参数
    var queryString = '';
    if (params && typeof params === 'object') {
        var paramArray = [];
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
                paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
            }
        }
        queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', baseUrl + url + queryString, true);
    xhr.timeout = 30000;
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '解析响应数据失败',
                            rows: [],
                            total: 0
                        });
                    }
                }
            } else {
                console.error('请求失败:', xhr.status, xhr.statusText);
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                        rows: [],
                        total: 0
                    });
                }
            }
        }
    };

    xhr.ontimeout = function() {
        console.error('请求超时');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '请求超时',
                rows: [],
                total: 0
            });
        }
    };

    xhr.onerror = function() {
        console.error('请求发生错误');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '网络错误',
                rows: [],
                total: 0
            });
        }
    };

    xhr.send();
}

// 页面加载完成后初始化
$(function() {
    initInstitutionRecruitmentIndexPage();
});
