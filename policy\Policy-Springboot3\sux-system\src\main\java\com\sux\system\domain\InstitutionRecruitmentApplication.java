package com.sux.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 机构招募申请对象 institution_recruitment_application
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
public class InstitutionRecruitmentApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 招募ID */
    @Excel(name = "招募ID")
    private Long recruitmentId;

    /** 招募标题 */
    @Excel(name = "招募标题")
    private String recruitmentTitle;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String institutionName;

    /** 机构代码 */
    @Excel(name = "机构代码")
    private String institutionCode;

    /** 法定代表人 */
    @Excel(name = "法定代表人")
    private String legalPerson;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 机构地址 */
    @Excel(name = "机构地址")
    private String institutionAddress;

    /** 机构类型 */
    @Excel(name = "机构类型")
    private String institutionType;

    /** 成立时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "成立时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date establishedDate;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private BigDecimal registeredCapital;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 培训经验 */
    @Excel(name = "培训经验")
    private String trainingExperience;

    /** 培训能力 */
    @Excel(name = "培训能力")
    private String trainingCapacity;

    /** 培训计划 */
    @Excel(name = "培训计划")
    private String trainingPlan;

    /** 师资信息 */
    @Excel(name = "师资信息")
    private String teacherInfo;

    /** 设施信息 */
    @Excel(name = "设施信息")
    private String facilityInfo;

    /** 资质文件 */
    @Excel(name = "资质文件")
    private String qualificationFiles;

    /** 培训计划文件 */
    @Excel(name = "培训计划文件")
    private String trainingPlanFile;

    /** 师资证书文件 */
    @Excel(name = "师资证书文件")
    private String teacherCertFiles;

    /** 设施文件 */
    @Excel(name = "设施文件")
    private String facilityFiles;

    /** 其他文件 */
    @Excel(name = "其他文件")
    private String otherFiles;

    /** 申请状态（0待审核 1已通过 2已拒绝 3已取消） */
    @Excel(name = "申请状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝,3=已取消")
    private String applicationStatus;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核人 */
    @Excel(name = "审核人")
    private String reviewer;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 申请备注 */
    @Excel(name = "申请备注")
    private String applicationNote;

    /** 申请用户ID */
    @Excel(name = "申请用户ID")
    private Long applicantUserId;

    /** 是否选中（0否 1是） */
    @Excel(name = "是否选中", readConverterExp = "0=否,1=是")
    private String isSelected;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }
    public void setRecruitmentId(Long recruitmentId) 
    {
        this.recruitmentId = recruitmentId;
    }

    public Long getRecruitmentId() 
    {
        return recruitmentId;
    }
    public void setRecruitmentTitle(String recruitmentTitle) 
    {
        this.recruitmentTitle = recruitmentTitle;
    }

    public String getRecruitmentTitle() 
    {
        return recruitmentTitle;
    }
    public void setInstitutionName(String institutionName) 
    {
        this.institutionName = institutionName;
    }

    public String getInstitutionName() 
    {
        return institutionName;
    }
    public void setInstitutionCode(String institutionCode) 
    {
        this.institutionCode = institutionCode;
    }

    public String getInstitutionCode() 
    {
        return institutionCode;
    }
    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setInstitutionAddress(String institutionAddress) 
    {
        this.institutionAddress = institutionAddress;
    }

    public String getInstitutionAddress() 
    {
        return institutionAddress;
    }
    public void setInstitutionType(String institutionType) 
    {
        this.institutionType = institutionType;
    }

    public String getInstitutionType() 
    {
        return institutionType;
    }
    public void setEstablishedDate(Date establishedDate) 
    {
        this.establishedDate = establishedDate;
    }

    public Date getEstablishedDate() 
    {
        return establishedDate;
    }
    public void setRegisteredCapital(BigDecimal registeredCapital) 
    {
        this.registeredCapital = registeredCapital;
    }

    public BigDecimal getRegisteredCapital() 
    {
        return registeredCapital;
    }
    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }
    public void setTrainingExperience(String trainingExperience) 
    {
        this.trainingExperience = trainingExperience;
    }

    public String getTrainingExperience() 
    {
        return trainingExperience;
    }
    public void setTrainingCapacity(String trainingCapacity) 
    {
        this.trainingCapacity = trainingCapacity;
    }

    public String getTrainingCapacity() 
    {
        return trainingCapacity;
    }
    public void setTrainingPlan(String trainingPlan) 
    {
        this.trainingPlan = trainingPlan;
    }

    public String getTrainingPlan() 
    {
        return trainingPlan;
    }
    public void setTeacherInfo(String teacherInfo) 
    {
        this.teacherInfo = teacherInfo;
    }

    public String getTeacherInfo() 
    {
        return teacherInfo;
    }
    public void setFacilityInfo(String facilityInfo) 
    {
        this.facilityInfo = facilityInfo;
    }

    public String getFacilityInfo() 
    {
        return facilityInfo;
    }
    public void setQualificationFiles(String qualificationFiles) 
    {
        this.qualificationFiles = qualificationFiles;
    }

    public String getQualificationFiles() 
    {
        return qualificationFiles;
    }
    public void setTrainingPlanFile(String trainingPlanFile) 
    {
        this.trainingPlanFile = trainingPlanFile;
    }

    public String getTrainingPlanFile() 
    {
        return trainingPlanFile;
    }
    public void setTeacherCertFiles(String teacherCertFiles) 
    {
        this.teacherCertFiles = teacherCertFiles;
    }

    public String getTeacherCertFiles() 
    {
        return teacherCertFiles;
    }
    public void setFacilityFiles(String facilityFiles) 
    {
        this.facilityFiles = facilityFiles;
    }

    public String getFacilityFiles() 
    {
        return facilityFiles;
    }
    public void setOtherFiles(String otherFiles) 
    {
        this.otherFiles = otherFiles;
    }

    public String getOtherFiles() 
    {
        return otherFiles;
    }
    public void setApplicationStatus(String applicationStatus) 
    {
        this.applicationStatus = applicationStatus;
    }

    public String getApplicationStatus() 
    {
        return applicationStatus;
    }
    public void setApplicationTime(Date applicationTime) 
    {
        this.applicationTime = applicationTime;
    }

    public Date getApplicationTime() 
    {
        return applicationTime;
    }
    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }
    public void setReviewer(String reviewer) 
    {
        this.reviewer = reviewer;
    }

    public String getReviewer() 
    {
        return reviewer;
    }
    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }
    public void setApplicationNote(String applicationNote) 
    {
        this.applicationNote = applicationNote;
    }

    public String getApplicationNote() 
    {
        return applicationNote;
    }
    public void setApplicantUserId(Long applicantUserId) 
    {
        this.applicantUserId = applicantUserId;
    }

    public Long getApplicantUserId() 
    {
        return applicantUserId;
    }
    public void setIsSelected(String isSelected) 
    {
        this.isSelected = isSelected;
    }

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public String getIsSelected() 
    {
        return isSelected;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("recruitmentId", getRecruitmentId())
            .append("recruitmentTitle", getRecruitmentTitle())
            .append("institutionName", getInstitutionName())
            .append("institutionCode", getInstitutionCode())
            .append("legalPerson", getLegalPerson())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("institutionAddress", getInstitutionAddress())
            .append("institutionType", getInstitutionType())
            .append("establishedDate", getEstablishedDate())
            .append("registeredCapital", getRegisteredCapital())
            .append("businessScope", getBusinessScope())
            .append("trainingExperience", getTrainingExperience())
            .append("trainingCapacity", getTrainingCapacity())
            .append("trainingPlan", getTrainingPlan())
            .append("teacherInfo", getTeacherInfo())
            .append("facilityInfo", getFacilityInfo())
            .append("qualificationFiles", getQualificationFiles())
            .append("trainingPlanFile", getTrainingPlanFile())
            .append("teacherCertFiles", getTeacherCertFiles())
            .append("facilityFiles", getFacilityFiles())
            .append("otherFiles", getOtherFiles())
            .append("applicationStatus", getApplicationStatus())
            .append("applicationTime", getApplicationTime())
            .append("reviewTime", getReviewTime())
            .append("reviewer", getReviewer())
            .append("reviewComment", getReviewComment())
            .append("applicationNote", getApplicationNote())
            .append("applicantUserId", getApplicantUserId())
            .append("isSelected", getIsSelected())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
